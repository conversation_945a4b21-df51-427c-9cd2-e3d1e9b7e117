apiVersion: apps/v1
kind: Deployment
metadata:
  name: claims-customer
  namespace: claims-demo
  labels:
    app: claims-customer
spec:
  replicas: 1
  strategy:
    type: RollingUpdate
    rollingUpdate:
      maxSurge: 1
      maxUnavailable: 0
  selector:
    matchLabels:
      app: claims-customer
  template:
    metadata:
      labels:
        app: claims-customer
      annotations:
        # This annotation ensures Kubernetes creates a new pod on each deployment
        # The value will be replaced with the commit SHA during deployment
        kubernetes.io/change-cause: "Image updated to ${IMAGE_TAG} at $(date)"
    spec:
      imagePullSecrets:
        - name: docker-registry-secret
      containers:
        - name: claims-customer
          image: reg.uniphoredemos.com/claims-customer:latest
          imagePullPolicy: Always
          ports:
            - containerPort: 3000
          envFrom:
            - configMapRef:
                name: claims-customer-config
          resources:
            limits:
              cpu: "500m"
              memory: "512Mi"
            requests:
              cpu: "200m"
              memory: "256Mi"
          readinessProbe:
            httpGet:
              path: /
              port: 3000
            initialDelaySeconds: 10
            periodSeconds: 5
          livenessProbe:
            httpGet:
              path: /
              port: 3000
            initialDelaySeconds: 20
            periodSeconds: 15
          volumeMounts:
            - name: uploads
              mountPath: /app/uploads
      volumes:
        - name: uploads
          persistentVolumeClaim:
            claimName: claims-customer-uploads-pvc
