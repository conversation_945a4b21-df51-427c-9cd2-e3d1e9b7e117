apiVersion: v1
kind: ConfigMap
metadata:
  name: postgres-init-config
  namespace: claims-demo
data:
  init.sql: |
    -- PostgreSQL initialization script for Claims Customer application
    -- This script sets up the database with proper permissions and extensions

    -- Connect to the database
    \c claims_customer;

    -- Create extensions that might be useful
    CREATE EXTENSION IF NOT EXISTS "uuid-ossp";
    CREATE EXTENSION IF NOT EXISTS "pg_trgm";

    -- Grant necessary permissions to the user
    GRANT ALL PRIVILEGES ON DATABASE claims_customer TO claims_user;
    GRANT ALL ON SCHEMA public TO claims_user;
    GRANT ALL PRIVILEGES ON ALL TABLES IN SCHEMA public TO claims_user;
    GRANT ALL PRIVILEGES ON ALL SEQUENCES IN SCHEMA public TO claims_user;

    -- Set default privileges for future objects
    ALTER DEFAULT PRIVILEGES IN SCHEMA public GRANT ALL ON TABLES TO claims_user;
    ALTER DEFAULT PRIVILEGES IN SCHEMA public GRANT ALL ON SEQUENCES TO claims_user;

    -- Log successful initialization
    SELECT 'PostgreSQL database initialized successfully for Claims Customer application' AS status;
