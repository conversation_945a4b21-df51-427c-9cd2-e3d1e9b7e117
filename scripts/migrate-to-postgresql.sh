#!/bin/bash
set -e

# PostgreSQL Migration Script for Claims Customer Application
# This script helps migrate from SQLite to PostgreSQL

echo "🚀 Claims Customer PostgreSQL Migration Script"
echo "=============================================="

# Function to check if command exists
command_exists() {
    command -v "$1" >/dev/null 2>&1
}

# Check prerequisites
echo "📋 Checking prerequisites..."

if ! command_exists podman; then
    echo "❌ Docker is not installed. Please install Docker first."
    exit 1
fi

echo "✅ Prerequisites check passed"

# Parse command line arguments
ENVIRONMENT="local"
SKIP_BACKUP="false"
FORCE="false"

while [[ $# -gt 0 ]]; do
    case $1 in
    --environment)
        ENVIRONMENT="$2"
        shift 2
        ;;
    --skip-backup)
        SKIP_BACKUP="true"
        shift
        ;;
    --force)
        FORCE="true"
        shift
        ;;
    --help)
        echo "Usage: $0 [OPTIONS]"
        echo ""
        echo "Options:"
        echo "  --environment ENV    Target environment (local|kubernetes) [default: local]"
        echo "  --skip-backup       Skip SQLite backup creation"
        echo "  --force             Force migration without confirmation"
        echo "  --help              Show this help message"
        exit 0
        ;;
    *)
        echo "Unknown option: $1"
        echo "Use --help for usage information"
        exit 1
        ;;
    esac
done

echo "🎯 Target environment: $ENVIRONMENT"

# Backup existing SQLite database if it exists
if [ "$SKIP_BACKUP" = "false" ] && [ -f "frontend/prisma/dev.db" ]; then
    echo "💾 Creating backup of existing SQLite database..."
    BACKUP_DIR="backups/$(date +%Y%m%d_%H%M%S)"
    mkdir -p "$BACKUP_DIR"
    cp frontend/prisma/dev.db "$BACKUP_DIR/dev.db.backup"
    echo "✅ Backup created at: $BACKUP_DIR/dev.db.backup"
fi

# Confirmation prompt
if [ "$FORCE" = "false" ]; then
    echo ""
    echo "⚠️  This will migrate your application from SQLite to PostgreSQL."
    echo "   Make sure you have backed up any important data."
    echo ""
    read -p "Do you want to continue? (y/N): " -n 1 -r
    echo
    if [[ ! $REPLY =~ ^[Yy]$ ]]; then
        echo "❌ Migration cancelled"
        exit 1
    fi
fi

if [ "$ENVIRONMENT" = "local" ]; then
    echo "🐳 Starting local PostgreSQL migration..."

    # Stop any existing containers
    echo "🛑 Stopping existing containers..."
    podman compose down || true

    # Start PostgreSQL
    echo "🚀 Starting PostgreSQL..."
    podman compose up -d postgres

    # Wait for PostgreSQL to be ready
    echo "⏳ Waiting for PostgreSQL to be ready..."
    timeout=60
    while ! podman compose exec postgres pg_isready -U claims_user -d claims_customer >/dev/null 2>&1; do
        if [ $timeout -le 0 ]; then
            echo "❌ PostgreSQL failed to start within 60 seconds"
            exit 1
        fi
        echo "   PostgreSQL is starting... ($timeout seconds remaining)"
        sleep 2
        timeout=$((timeout - 2))
    done

    echo "✅ PostgreSQL is ready!"

    # Generate Prisma client and run migrations
    echo "🔄 Generating Prisma client and running migrations..."
    cd frontend

    # Install dependencies if needed
    if [ ! -d "node_modules" ]; then
        echo "📦 Installing dependencies..."
        npm install --legacy-peer-deps
    fi

    # Generate Prisma client
    npx prisma generate

    # Push schema to database
    npx prisma db push --accept-data-loss

    echo "✅ Database schema applied successfully!"

    # Start the application
    # echo "🚀 Starting the application..."
    # cd ..
    # podman compose up -d claims-app

    echo ""
    echo "🎉 Migration completed successfully!"
    echo "📱 Application is available at: http://localhost:3000"
    echo "🗄️  PostgreSQL is available at: localhost:5432"
    echo ""
    echo "Useful commands:"
    echo "  docker-compose logs -f          # View logs"
    echo "  docker-compose down             # Stop services"
    echo "  docker-compose exec postgres psql -U claims_user claims_customer  # Connect to database"

elif [ "$ENVIRONMENT" = "kubernetes" ]; then
    echo "☸️  Starting Kubernetes PostgreSQL migration..."

    # Check if kubectl is available
    if ! command_exists kubectl; then
        echo "❌ kubectl is not installed. Please install kubectl first."
        exit 1
    fi

    # Apply PostgreSQL resources
    echo "🚀 Deploying PostgreSQL to Kubernetes..."
    kubectl apply -f k8s/namespace.yaml
    kubectl apply -f k8s/postgres-init-configmap.yaml
    kubectl apply -f k8s/postgres-pvc.yaml
    kubectl apply -f k8s/postgres-deployment.yaml

    # Wait for PostgreSQL to be ready
    echo "⏳ Waiting for PostgreSQL to be ready..."
    kubectl wait --for=condition=ready pod -l app=postgres -n claims-demo --timeout=300s

    # Update application configuration
    echo "🔄 Updating application configuration..."
    kubectl apply -f k8s/configmap.yaml
    kubectl apply -f k8s/deployment.yaml

    echo "✅ Kubernetes migration completed!"
    echo "📱 Check application status with: kubectl get pods -n claims-demo"

else
    echo "❌ Unknown environment: $ENVIRONMENT"
    echo "Supported environments: local, kubernetes"
    exit 1
fi

echo ""
echo "📚 For more information, see POSTGRESQL-MIGRATION.md"
