#!/bin/bash
set -e

# PostgreSQL Test Script for Claims Customer Application
# This script tests the PostgreSQL setup and basic functionality

echo "🧪 PostgreSQL Test Suite for Claims Customer"
echo "============================================"

# Function to check if command exists
command_exists() {
    command -v "$1" >/dev/null 2>&1
}

# Function to test database connection
test_db_connection() {
    echo "🔌 Testing database connection..."
    
    if docker-compose exec postgres pg_isready -U claims_user -d claims_customer >/dev/null 2>&1; then
        echo "✅ Database connection successful"
        return 0
    else
        echo "❌ Database connection failed"
        return 1
    fi
}

# Function to test basic CRUD operations
test_crud_operations() {
    echo "🔄 Testing basic CRUD operations..."
    
    # Test creating a table and inserting data
    docker-compose exec postgres psql -U claims_user -d claims_customer -c "
        -- Test table creation
        CREATE TABLE IF NOT EXISTS test_table (
            id SERIAL PRIMARY KEY,
            name VARCHA<PERSON>(100),
            created_at TIMESTAMP DEFAULT NOW()
        );
        
        -- Test insert
        INSERT INTO test_table (name) VALUES ('Test Employee');
        
        -- Test select
        SELECT COUNT(*) as test_count FROM test_table WHERE name = 'Test Employee';
        
        -- Test update
        UPDATE test_table SET name = 'Updated Test Employee' WHERE name = 'Test Employee';
        
        -- Test delete
        DELETE FROM test_table WHERE name = 'Updated Test Employee';
        
        -- Clean up
        DROP TABLE test_table;
    " >/dev/null 2>&1
    
    if [ $? -eq 0 ]; then
        echo "✅ CRUD operations successful"
        return 0
    else
        echo "❌ CRUD operations failed"
        return 1
    fi
}

# Function to test Prisma schema
test_prisma_schema() {
    echo "🏗️  Testing Prisma schema..."
    
    cd frontend
    
    # Check if schema can be validated
    if npx prisma validate >/dev/null 2>&1; then
        echo "✅ Prisma schema validation successful"
    else
        echo "❌ Prisma schema validation failed"
        cd ..
        return 1
    fi
    
    # Check if client can be generated
    if npx prisma generate >/dev/null 2>&1; then
        echo "✅ Prisma client generation successful"
    else
        echo "❌ Prisma client generation failed"
        cd ..
        return 1
    fi
    
    cd ..
    return 0
}

# Function to test application endpoints
test_application_endpoints() {
    echo "🌐 Testing application endpoints..."
    
    # Wait for application to be ready
    timeout=30
    while ! curl -f http://localhost:3000 >/dev/null 2>&1; do
        if [ $timeout -le 0 ]; then
            echo "❌ Application failed to start within 30 seconds"
            return 1
        fi
        echo "   Waiting for application... ($timeout seconds remaining)"
        sleep 2
        timeout=$((timeout-2))
    done
    
    # Test main page
    if curl -f http://localhost:3000 >/dev/null 2>&1; then
        echo "✅ Main page accessible"
    else
        echo "❌ Main page not accessible"
        return 1
    fi
    
    # Test API endpoints
    if curl -f http://localhost:3000/api/employees >/dev/null 2>&1; then
        echo "✅ Employees API accessible"
    else
        echo "❌ Employees API not accessible"
        return 1
    fi
    
    if curl -f http://localhost:3000/api/claims >/dev/null 2>&1; then
        echo "✅ Claims API accessible"
    else
        echo "❌ Claims API not accessible"
        return 1
    fi
    
    return 0
}

# Function to test data persistence
test_data_persistence() {
    echo "💾 Testing data persistence..."
    
    # Create test data
    TEST_RESULT=$(docker-compose exec postgres psql -U claims_user -d claims_customer -t -c "
        INSERT INTO \"Employee\" (\"firstName\", \"lastName\", \"email\") 
        VALUES ('Test', 'User', '<EMAIL>') 
        ON CONFLICT (\"email\") DO UPDATE SET \"firstName\" = 'Test'
        RETURNING id;
    " 2>/dev/null | tr -d ' ')
    
    if [ ! -z "$TEST_RESULT" ]; then
        echo "✅ Data insertion successful"
        
        # Verify data exists
        COUNT=$(docker-compose exec postgres psql -U claims_user -d claims_customer -t -c "
            SELECT COUNT(*) FROM \"Employee\" WHERE \"email\" = '<EMAIL>';
        " 2>/dev/null | tr -d ' ')
        
        if [ "$COUNT" = "1" ]; then
            echo "✅ Data persistence verified"
            
            # Clean up test data
            docker-compose exec postgres psql -U claims_user -d claims_customer -c "
                DELETE FROM \"Employee\" WHERE \"email\" = '<EMAIL>';
            " >/dev/null 2>&1
            
            return 0
        else
            echo "❌ Data persistence failed"
            return 1
        fi
    else
        echo "❌ Data insertion failed"
        return 1
    fi
}

# Main test execution
echo "🚀 Starting PostgreSQL tests..."

# Check if Docker Compose is running
if ! docker-compose ps postgres | grep -q "Up"; then
    echo "❌ PostgreSQL container is not running"
    echo "💡 Run 'docker-compose up -d postgres' first"
    exit 1
fi

# Run tests
FAILED_TESTS=0

test_db_connection || FAILED_TESTS=$((FAILED_TESTS + 1))
test_crud_operations || FAILED_TESTS=$((FAILED_TESTS + 1))
test_prisma_schema || FAILED_TESTS=$((FAILED_TESTS + 1))

# Check if application is running for endpoint tests
if docker-compose ps claims-app | grep -q "Up"; then
    test_application_endpoints || FAILED_TESTS=$((FAILED_TESTS + 1))
    test_data_persistence || FAILED_TESTS=$((FAILED_TESTS + 1))
else
    echo "⚠️  Application container not running, skipping endpoint tests"
    echo "💡 Run 'docker-compose up -d' to test the full application"
fi

# Summary
echo ""
echo "📊 Test Summary"
echo "==============="

if [ $FAILED_TESTS -eq 0 ]; then
    echo "🎉 All tests passed! PostgreSQL setup is working correctly."
    exit 0
else
    echo "❌ $FAILED_TESTS test(s) failed. Please check the configuration."
    echo ""
    echo "🔧 Troubleshooting tips:"
    echo "  - Check Docker Compose logs: docker-compose logs"
    echo "  - Verify environment variables in .env file"
    echo "  - Ensure PostgreSQL is fully initialized"
    echo "  - Check network connectivity between containers"
    exit 1
fi
