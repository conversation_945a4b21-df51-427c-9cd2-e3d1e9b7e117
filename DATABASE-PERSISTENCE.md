# Database Persistence in Kubernetes

This document explains how database persistence is implemented in the Claims Customer application when deployed to Kubernetes.

## Overview

The Claims Customer application uses PostgreSQL as its database. To ensure data persistence across deployments and pod restarts, we use a Kubernetes PersistentVolumeClaim (PVC) to store the PostgreSQL data directory.

## Implementation Details

### PersistentVolumeClaim

The database file is stored in a PVC named `claims-customer-database-pvc`, which is defined in `k8s/database-pvc.yaml`. This PVC is mounted to the pod at `/app/prisma/db`.

```yaml
apiVersion: v1
kind: PersistentVolumeClaim
metadata:
  name: claims-customer-database-pvc
  namespace: claims-demo
  annotations:
    "kubernetes.io/description": "Persistent storage for SQLite database - DO NOT DELETE"
spec:
  accessModes:
    - ReadWriteOnce
  resources:
    requests:
      storage: 1Gi
```

### Deployment Configuration

The deployment configuration in `k8s/deployment.yaml` mounts the PVC to the pod:

```yaml
volumeMounts:
  - name: database
    mountPath: /app/prisma/db
    subPath: db
volumes:
  - name: database
    persistentVolumeClaim:
      claimName: claims-customer-database-pvc
```

### Database Initialization

When the pod starts, the `init-db-directory.sh` script is executed to ensure the database directory structure exists and has the correct permissions. This script only creates an empty database file if one doesn't already exist, ensuring that existing data is preserved.

### Database Migrations

The application uses Prisma for database migrations. When the pod starts, the `docker-entrypoint.sh` script runs `prisma migrate deploy` to apply any pending migrations to the database. This ensures that the database schema is up-to-date with the application code.

## Deployment Strategy

To ensure data persistence across deployments, we use a rolling update strategy:

1. The CI/CD pipeline applies the PVC before the deployment
2. The deployment uses a rolling update strategy to ensure that the new pod is created before the old one is terminated
3. The new pod mounts the same PVC, ensuring that the database file is preserved

## Troubleshooting

If you experience data loss after a deployment, check the following:

1. Verify that the PVC exists and is bound to a volume:
   ```
   kubectl get pvc -n claims-demo
   ```

2. Check the pod logs for any errors during database initialization or migration:
   ```
   kubectl logs -n claims-demo <pod-name>
   ```

3. Verify that the database file exists in the pod:
   ```
   kubectl exec -it -n claims-demo <pod-name> -- ls -la /app/prisma/db
   ```

4. If the database file exists but is empty, check if the migrations are running correctly:
   ```
   kubectl exec -it -n claims-demo <pod-name> -- npx prisma migrate status
   ```

## Backup and Restore

### Creating a Backup

To create a backup of the database, you can copy the database file from the pod:

```bash
# Get the pod name
kubectl get pods -n claims-demo

# Copy the database file from the pod
kubectl cp claims-demo/<pod-name>:/app/prisma/db/dev.db ./dev.db.backup
```

### Restoring from a Backup

To restore from a backup, you can copy the backup file to the pod:

```bash
# Copy the backup file to the pod
kubectl cp ./dev.db.backup claims-demo/<pod-name>:/app/prisma/db/dev.db

# Restart the pod to ensure the application uses the restored database
kubectl delete pod -n claims-demo <pod-name>
```

## Best Practices

1. Always test database migrations locally before deploying to production
2. Create regular backups of the database
3. Use a rolling update strategy for deployments to ensure data persistence
4. Monitor the application logs for any database-related errors
