# PostgreSQL Migration Guide

This document provides instructions for running the Claims Customer application with PostgreSQL instead of SQLite.

## Overview

The application has been migrated from SQLite to PostgreSQL for better scalability, performance, and production readiness. This migration includes:

- Updated Prisma schema to use PostgreSQL provider
- Docker Compose setup for local development
- Kubernetes configuration for production deployment
- Updated environment variables and connection strings

## Prerequisites

- Docker and Docker Compose installed
- Node.js 20+ (for local development)
- kube<PERSON><PERSON> configured (for Kubernetes deployment)

## Local Development Setup

### 1. Using Docker Compose (Recommended)

Start the entire application stack with PostgreSQL:

```bash
# Start PostgreSQL and the application
docker-compose up -d

# View logs
docker-compose logs -f

# Stop the services
docker-compose down
```

The application will be available at `http://localhost:3000` and PostgreSQL at `localhost:5432`.

### 2. Local Development with External PostgreSQL

If you prefer to run the application locally while using a containerized PostgreSQL:

```bash
# Start only PostgreSQL
docker-compose up -d postgres

# Install dependencies
cd frontend
npm install --legacy-peer-deps

# Run database migrations
npx prisma migrate deploy

# Start the development server
npm run dev
```

## Environment Variables

### Local Development (.env)
```
DATABASE_URL="postgresql://claims_user:claims_password@localhost:5432/claims_customer?schema=public"
```

### Production/Kubernetes
```
DATABASE_URL="**************************************************************/claims_customer?schema=public"
```

## Database Configuration

### PostgreSQL Connection Details
- **Host**: localhost (local) / postgres-service (Kubernetes)
- **Port**: 5432
- **Database**: claims_customer
- **Username**: claims_user
- **Password**: claims_password

### Database Schema
The PostgreSQL database includes the same schema as the previous SQLite setup:
- Employee management
- Claims processing
- Policy management
- Auto loan applications
- Document storage

## Kubernetes Deployment

### 1. Deploy PostgreSQL

```bash
# Create namespace
kubectl apply -f k8s/namespace.yaml

# Deploy PostgreSQL components
kubectl apply -f k8s/postgres-init-configmap.yaml
kubectl apply -f k8s/postgres-pvc.yaml
kubectl apply -f k8s/postgres-deployment.yaml

# Wait for PostgreSQL to be ready
kubectl wait --for=condition=ready pod -l app=postgres -n claims-demo --timeout=300s
```

### 2. Deploy Application

```bash
# Update ConfigMap with PostgreSQL connection
kubectl apply -f k8s/configmap.yaml

# Deploy the application
kubectl apply -f k8s/deployment.yaml
kubectl apply -f k8s/service.yaml
kubectl apply -f k8s/ingress.yaml
```

## Migration from SQLite

If you have existing SQLite data that needs to be migrated:

### 1. Export SQLite Data

```bash
# Connect to the old SQLite database
sqlite3 frontend/prisma/dev.db

# Export data to SQL
.output data_export.sql
.dump

# Or export specific tables
.mode insert Employee
.output employees.sql
SELECT * FROM Employee;
```

### 2. Import to PostgreSQL

```bash
# Connect to PostgreSQL
psql -h localhost -U claims_user -d claims_customer

# Import the data (after cleaning up SQLite-specific syntax)
\i cleaned_data_export.sql
```

## Troubleshooting

### Connection Issues

1. **PostgreSQL not ready**: Wait for the database to fully initialize
2. **Connection refused**: Check if PostgreSQL service is running
3. **Authentication failed**: Verify username/password in connection string

### Migration Issues

1. **Schema mismatch**: Run `npx prisma db push` to sync schema
2. **Missing tables**: Ensure migrations are applied with `npx prisma migrate deploy`
3. **Permission errors**: Check PostgreSQL user permissions

### Useful Commands

```bash
# Check PostgreSQL status
docker-compose ps postgres

# View PostgreSQL logs
docker-compose logs postgres

# Connect to PostgreSQL directly
docker-compose exec postgres psql -U claims_user -d claims_customer

# Reset database (development only)
docker-compose down -v
docker-compose up -d postgres
```

## Performance Considerations

- PostgreSQL provides better performance for concurrent operations
- Connection pooling is handled by Prisma
- Database indexes are maintained from the original schema
- Consider adding additional indexes for frequently queried fields

## Backup and Restore

### Backup
```bash
# Create database backup
docker-compose exec postgres pg_dump -U claims_user claims_customer > backup.sql
```

### Restore
```bash
# Restore from backup
docker-compose exec -T postgres psql -U claims_user claims_customer < backup.sql
```

## Security Notes

- Change default passwords in production
- Use environment variables for sensitive configuration
- Enable SSL/TLS for production connections
- Regularly update PostgreSQL version for security patches
