#!/bin/sh
set -e

# Wait for PostgreSQL to be ready
echo "Waiting for PostgreSQL to be ready..."
until npx prisma db push --schema=./prisma/schema.prisma --accept-data-loss --skip-generate 2>/dev/null; do
  echo "PostgreSQL is unavailable - sleeping"
  sleep 2
done
echo "PostgreSQL is ready!"

# Generate Prisma client
echo "Generating Prisma client..."
npx prisma generate --schema=./prisma/schema.prisma

# Run database migrations
echo "Running database migrations..."
if ! npx prisma migrate deploy --schema=./prisma/schema.prisma; then
  echo "ERROR: Database migration failed!"
  echo "Attempting to push schema directly..."
  if npx prisma db push --schema=./prisma/schema.prisma --accept-data-loss; then
    echo "Schema pushed successfully."
  else
    echo "Schema push also failed. Continuing anyway..."
  fi
else
  echo "Database migrations completed successfully."
fi

# Start the application
echo "Starting the application..."
exec "$@"
