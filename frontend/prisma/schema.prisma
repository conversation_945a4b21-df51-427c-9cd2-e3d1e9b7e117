// This is your Prisma schema file,
// learn more about it in the docs: https://pris.ly/d/prisma-schema

generator client {
  provider = "prisma-client-js"
  output   = "../src/generated/prisma"
}

datasource db {
  provider = "postgresql"
  url      = env("DATABASE_URL")
}

model Employee {
  id           Int        @id @default(autoincrement())
  firstName    String
  lastName     String
  email        String     @unique
  phone        String?
  address      String?
  employerName String?
  groupId      String?
  memberId     String?
  claims       Claim[]
  policy       Policy?
  autoLoans    AutoLoan[]
}

model Claim {
  id                Int              @id @default(autoincrement())
  employee          Employee         @relation(fields: [employeeId], references: [id])
  employeeId        Int
  claimType         String           @default("Critical Illness")
  description       String
  incidentDate      DateTime?
  dateFiled         DateTime         @default(now())
  status            String
  documents         ClaimDocument[]
  comments          ClaimComment[]
}

model ClaimDocument {
  id         Int      @id @default(autoincrement())
  claim      Claim    @relation(fields: [claimId], references: [id])
  claimId    Int
  fileName   String
  filePath   String
  uploadedAt DateTime @default(now())
}

model ClaimComment {
  id        Int      @id @default(autoincrement())
  claim     Claim    @relation(fields: [claimId], references: [id])
  claimId   Int
  text      String
  createdAt DateTime @default(now())
}

model Policy {
  id                    Int             @id @default(autoincrement())
  employee              Employee        @relation(fields: [employeeId], references: [id])
  employeeId            Int             @unique
  policyOwner           String
  insured               String
  spouse                String?
  group                 String
  policyNumber          String
  originalEffectiveDate DateTime?
  scheduledEffectiveDate DateTime?
  issuedAge             Int?
  insuredCoverage       Float?
  spouseCoverage        Float?
  documents             PolicyDocument[]
}

model PolicyDocument {
  id         Int      @id @default(autoincrement())
  policy     Policy   @relation(fields: [policyId], references: [id])
  policyId   Int
  fileName   String
  filePath   String
  uploadedAt DateTime @default(now())
}

model AutoLoan {
  id                Int                @id @default(autoincrement())
  employee          Employee           @relation(fields: [employeeId], references: [id])
  employeeId        Int
  // Personal details (using Employee relation)
  dniNumber         String
  dateOfBirth       DateTime
  incomeSource      String             // Salaried, Self-Employed, Pension
  // Finance details
  loanAmount        Float
  totalPrice        Float
  tradeInValue      Float
  downPayment       Float
  remainingPrice    Float
  // Car details
  niv               String
  newPreowned       String             // New, Preowned
  year              Int
  make              String
  model             String
  trim              String
  // Metadata
  applicationDate   DateTime           @default(now())
  status            String             @default("submitted")
  documents         AutoLoanDocument[]
}

model AutoLoanDocument {
  id         Int      @id @default(autoincrement())
  autoLoan   AutoLoan @relation(fields: [autoLoanId], references: [id])
  autoLoanId Int
  fileName   String
  filePath   String
  uploadedAt DateTime @default(now())
}
