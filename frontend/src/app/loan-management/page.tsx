"use client";

import { useState, useEffect, useMemo } from "react";
import { useRouter } from "next/navigation";
import Link from "next/link";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { CardHeader, CardTitle } from "@/components/ui/card";

interface Employee {
  id: number;
  firstName: string;
  lastName: string;
  email: string;
  phone: string | null;
  address: string | null;
  employerName: string | null;
  groupId: string | null;
  memberId: string | null;
}

interface AutoLoanDocument {
  id: number;
  autoLoanId: number;
  fileName: string;
  filePath: string;
  uploadedAt: string;
}

interface AutoLoan {
  id: number;
  employeeId: number;
  employee: Employee;
  dniNumber: string;
  dateOfBirth: string;
  incomeSource: string;
  loanAmount: number;
  totalPrice: number;
  tradeInValue: number;
  downPayment: number;
  remainingPrice: number;
  niv: string;
  newPreowned: string;
  year: number;
  make: string;
  model: string;
  trim: string;
  applicationDate: string;
  status: string;
  documents: AutoLoanDocument[];
}

export default function LoanManagementPage() {
  const router = useRouter();
  const [loans, setLoans] = useState<AutoLoan[]>([]);
  const [filteredLoans, setFilteredLoans] = useState<AutoLoan[]>([]);
  const [loading, setLoading] = useState(true);
  const [searchQuery, setSearchQuery] = useState<string>("");
  const [sortField, setSortField] = useState<keyof AutoLoan>("applicationDate");
  const [sortDirection, setSortDirection] = useState<"asc" | "desc">("desc");

  useEffect(() => {
    const fetchLoans = async () => {
      try {
        const response = await fetch("/api/auto-loans");
        if (!response.ok) {
          throw new Error("Failed to fetch loans");
        }
        const data = await response.json();
        setLoans(data);
        setFilteredLoans(data);
      } catch (error) {
        console.error("Error fetching loans:", error);
      } finally {
        setLoading(false);
      }
    };

    fetchLoans();
  }, []);

  const filteredAndSortedLoans = useMemo(() => {
    let filtered = loans;

    // Apply search filter
    if (searchQuery) {
      filtered = filtered.filter(
        (loan) =>
          loan.employee.firstName.toLowerCase().includes(searchQuery.toLowerCase()) ||
          loan.employee.lastName.toLowerCase().includes(searchQuery.toLowerCase()) ||
          loan.employee.email.toLowerCase().includes(searchQuery.toLowerCase()) ||
          loan.make.toLowerCase().includes(searchQuery.toLowerCase()) ||
          loan.model.toLowerCase().includes(searchQuery.toLowerCase()) ||
          loan.id.toString().includes(searchQuery)
      );
    }

    // Apply sorting
    filtered.sort((a, b) => {
      let aValue = a[sortField];
      let bValue = b[sortField];

      // Handle nested employee fields
      if (sortField === "employee") {
        aValue = `${a.employee.firstName} ${a.employee.lastName}`;
        bValue = `${b.employee.firstName} ${b.employee.lastName}`;
      }

      if (typeof aValue === "string" && typeof bValue === "string") {
        return sortDirection === "asc"
          ? aValue.localeCompare(bValue)
          : bValue.localeCompare(aValue);
      }

      if (typeof aValue === "number" && typeof bValue === "number") {
        return sortDirection === "asc" ? aValue - bValue : bValue - aValue;
      }

      return 0;
    });

    return filtered;
  }, [loans, searchQuery, sortField, sortDirection]);

  useEffect(() => {
    setFilteredLoans(filteredAndSortedLoans);
  }, [filteredAndSortedLoans]);

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString();
  };

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat("en-US", {
      style: "currency",
      currency: "USD",
    }).format(amount);
  };

  const getStatusClass = (status: string) => {
    const statusLower = status.toLowerCase();
    switch (statusLower) {
      case "submitted":
        return "bg-blue-100 text-blue-800 status-submitted";
      case "pending":
        return "bg-yellow-100 text-yellow-800 status-pending";
      case "approved":
        return "bg-green-100 text-green-800 status-approved";
      case "rejected":
        return "bg-red-100 text-red-800 status-rejected";
      default:
        return "bg-gray-100 text-gray-800";
    }
  };

  const handleRowClick = (loanId: number) => {
    router.push(`/loan-management/${loanId}`);
  };

  const handleSort = (field: keyof AutoLoan) => {
    if (field === sortField) {
      setSortDirection(sortDirection === "asc" ? "desc" : "asc");
    } else {
      setSortField(field);
      setSortDirection("asc");
    }
  };

  const getSortIcon = (field: keyof AutoLoan) => {
    if (sortField !== field) return "";
    return sortDirection === "asc" ? " ↑" : " ↓";
  };

  if (loading) {
    return (
      <div className="flex justify-center p-8">Loading loan applications...</div>
    );
  }

  return (
    <div className="space-y-6">
      <CardHeader className="px-0">
        <CardTitle>Loan Management</CardTitle>
      </CardHeader>

      <div className="flex flex-col md:flex-row gap-4 mb-6">
        <div className="flex-1">
          <Input
            placeholder="Search loans..."
            value={searchQuery}
            onChange={(e) => setSearchQuery(e.target.value)}
            className="w-full"
          />
        </div>
      </div>

      <div className="overflow-x-auto rounded-lg border">
        <table className="min-w-full divide-y divide-gray-200">
          <thead className="bg-gray-50">
            <tr>
              <th
                scope="col"
                className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer"
                onClick={() => handleSort("id")}
              >
                Loan ID {getSortIcon("id")}
              </th>
              <th
                scope="col"
                className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer"
                onClick={() => handleSort("employee" as keyof AutoLoan)}
              >
                Applicant {getSortIcon("employee" as keyof AutoLoan)}
              </th>
              <th
                scope="col"
                className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"
              >
                Vehicle
              </th>
              <th
                scope="col"
                className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer"
                onClick={() => handleSort("loanAmount")}
              >
                Loan Amount {getSortIcon("loanAmount")}
              </th>
              <th
                scope="col"
                className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer"
                onClick={() => handleSort("applicationDate")}
              >
                Application Date {getSortIcon("applicationDate")}
              </th>
              <th
                scope="col"
                className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer"
                onClick={() => handleSort("status")}
              >
                Status {getSortIcon("status")}
              </th>
              <th
                scope="col"
                className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"
              >
                Documents
              </th>
              <th
                scope="col"
                className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"
              >
                Actions
              </th>
            </tr>
          </thead>
          <tbody className="bg-white divide-y divide-gray-200">
            {filteredLoans.length > 0 ? (
              filteredLoans.map((loan) => (
                <tr
                  key={loan.id}
                  className="hover:bg-gray-50 cursor-pointer"
                  onClick={() => handleRowClick(loan.id)}
                >
                  <td className="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">
                    {loan.id}
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                    <div>{loan.employee.firstName} {loan.employee.lastName}</div>
                    <div className="text-xs text-gray-400">{loan.employee.email}</div>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                    <div>{loan.year} {loan.make} {loan.model}</div>
                    <div className="text-xs text-gray-400">{loan.trim} ({loan.newPreowned})</div>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                    {formatCurrency(loan.loanAmount)}
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                    {formatDate(loan.applicationDate)}
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <span className={`px-2 inline-flex text-xs leading-5 font-semibold rounded-full ${getStatusClass(loan.status)}`}>
                      {loan.status}
                    </span>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                    {loan.documents.length}
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                    <Link href={`/loan-management/${loan.id}`} onClick={(e) => e.stopPropagation()}>
                      <Button
                        variant="outline"
                        size="sm"
                        className="mr-2"
                      >
                        View Details
                      </Button>
                    </Link>
                  </td>
                </tr>
              ))
            ) : (
              <tr>
                <td colSpan={8} className="px-6 py-4 text-center text-sm text-gray-500">
                  No loans found matching your filters
                </td>
              </tr>
            )}
          </tbody>
        </table>
      </div>
    </div>
  );
}
