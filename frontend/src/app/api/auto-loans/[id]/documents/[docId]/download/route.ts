import { NextRequest, NextResponse } from "next/server";
import { prisma } from "@/lib/prisma";
import fs from "fs";
import path from "path";

/**
 * @swagger
 * /api/auto-loans/{id}/documents/{docId}/download:
 *   get:
 *     summary: Download a document file for an auto loan
 *     description: Downloads a specific document file associated with an auto loan application
 *     tags:
 *       - Auto Loans
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: integer
 *         description: Auto loan application ID
 *       - in: path
 *         name: docId
 *         required: true
 *         schema:
 *           type: integer
 *         description: Document ID
 *     responses:
 *       200:
 *         description: File downloaded successfully
 *         content:
 *           application/octet-stream:
 *             schema:
 *               type: string
 *               format: binary
 *       404:
 *         description: Document not found
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/Error'
 *       500:
 *         description: Server error
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/Error'
 */
export async function GET(
  req: NextRequest,
  context: { params: Promise<{ id: string; docId: string }> }
) {
  try {
    const params = await context.params;
    const loanId = Number(params.id);
    const docId = Number(params.docId);

    if (isNaN(loanId) || isNaN(docId)) {
      return NextResponse.json({ error: "Invalid ID" }, { status: 400 });
    }

    // Check if this is a view request or a download request
    const { searchParams } = new URL(req.url);
    const isViewRequest = searchParams.get('view') === 'true';

    // Find the document
    const document = await prisma.autoLoanDocument.findFirst({
      where: {
        id: docId,
        autoLoanId: loanId,
      },
    });

    if (!document) {
      return NextResponse.json({ error: "Document not found" }, { status: 404 });
    }

    // Check if file exists
    const filePath = path.join(process.cwd(), document.filePath);
    if (!fs.existsSync(filePath)) {
      return NextResponse.json({ error: "File not found on server" }, { status: 404 });
    }

    // Read the file
    const fileBuffer = fs.readFileSync(filePath);

    // Determine content type based on file extension
    const ext = path.extname(document.fileName).toLowerCase();
    let contentType = "application/octet-stream";

    switch (ext) {
      case ".pdf":
        contentType = "application/pdf";
        break;
      case ".jpg":
      case ".jpeg":
        contentType = "image/jpeg";
        break;
      case ".png":
        contentType = "image/png";
        break;
      case ".gif":
        contentType = "image/gif";
        break;
      case ".txt":
        contentType = "text/plain";
        break;
    }

    // Set appropriate Content-Disposition header based on request type
    const contentDisposition = isViewRequest
      ? `inline; filename="${document.fileName}"`
      : `attachment; filename="${document.fileName}"`;

    // Create headers object
    const headers = new Headers({
      "Content-Disposition": contentDisposition,
      "Content-Type": contentType,
    });

    // For PDFs being viewed, add additional headers to prevent download
    if (isViewRequest && ext === '.pdf') {
      headers.set("X-Content-Type-Options", "nosniff");
      headers.set("Cache-Control", "public, max-age=3600");
    }

    return new Response(fileBuffer, { headers });
  } catch (err) {
    console.error("Error downloading document:", err);
    const errorMessage = err instanceof Error ? err.message : 'Unknown error occurred';
    return NextResponse.json({ error: errorMessage }, { status: 500 });
  }
}
