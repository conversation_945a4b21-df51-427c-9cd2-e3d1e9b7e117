import { NextRequest, NextResponse } from "next/server";
import { prisma } from "@/lib/prisma";

/**
 * @swagger
 * /api/submit-auto-loan:
 *   post:
 *     summary: Submit a new auto loan application
 *     description: Creates a new auto loan application with employee information and documents
 *     tags:
 *       - Auto Loans
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               firstName:
 *                 type: string
 *                 description: First name of the applicant
 *               lastName:
 *                 type: string
 *                 description: Last name of the applicant
 *               email:
 *                 type: string
 *                 format: email
 *                 description: Email address of the applicant
 *               phone:
 *                 type: string
 *                 description: Phone number of the applicant
 *               address:
 *                 type: string
 *                 description: Address of the applicant
 *               dniNumber:
 *                 type: string
 *                 description: DNI number of the applicant
 *               dateOfBirth:
 *                 type: string
 *                 format: date
 *                 description: Date of birth of the applicant
 *               incomeSource:
 *                 type: string
 *                 enum: [Salaried, Self-Employed, Pension]
 *                 description: Source of income
 *               loanAmount:
 *                 type: number
 *                 description: Requested loan amount
 *               totalPrice:
 *                 type: number
 *                 description: Total price of the vehicle
 *               tradeInValue:
 *                 type: number
 *                 description: Trade-in value of existing vehicle
 *               downPayment:
 *                 type: number
 *                 description: Down payment amount
 *               remainingPrice:
 *                 type: number
 *                 description: Remaining price after trade-in and down payment
 *               niv:
 *                 type: string
 *                 description: Vehicle identification number
 *               newPreowned:
 *                 type: string
 *                 enum: [New, Preowned]
 *                 description: Whether the vehicle is new or preowned
 *               year:
 *                 type: integer
 *                 description: Year of the vehicle
 *               make:
 *                 type: string
 *                 description: Make of the vehicle
 *               model:
 *                 type: string
 *                 description: Model of the vehicle
 *               trim:
 *                 type: string
 *                 description: Trim level of the vehicle
 *               files:
 *                 type: array
 *                 items:
 *                   type: object
 *                   properties:
 *                     originalname:
 *                       type: string
 *                     path:
 *                       type: string
 *                 description: Array of uploaded files
 *             required:
 *               - firstName
 *               - lastName
 *               - email
 *               - dniNumber
 *               - dateOfBirth
 *               - incomeSource
 *               - loanAmount
 *               - totalPrice
 *               - tradeInValue
 *               - downPayment
 *               - remainingPrice
 *               - niv
 *               - newPreowned
 *               - year
 *               - make
 *               - model
 *               - trim
 *               - files
 *     responses:
 *       200:
 *         description: Auto loan application submitted successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                 autoLoan:
 *                   $ref: '#/components/schemas/AutoLoan'
 *       400:
 *         description: Bad request - missing required fields
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/Error'
 *       500:
 *         description: Server error
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/Error'
 */
export async function POST(req: NextRequest) {
  try {
    const body = await req.json();
    const {
      firstName,
      lastName,
      email,
      phone,
      address,
      dniNumber,
      dateOfBirth,
      incomeSource,
      loanAmount,
      totalPrice,
      tradeInValue,
      downPayment,
      remainingPrice,
      niv,
      newPreowned,
      year,
      make,
      model,
      trim,
      files,
    } = body;

    // Validate required fields
    if (
      !firstName ||
      !lastName ||
      !email ||
      !dniNumber ||
      !dateOfBirth ||
      !incomeSource ||
      !loanAmount ||
      !totalPrice ||
      !tradeInValue ||
      !remainingPrice ||
      !niv ||
      !newPreowned ||
      !year ||
      !make ||
      !model ||
      !trim ||
      !files ||
      !Array.isArray(files) ||
      files.length === 0
    ) {
      return NextResponse.json(
        { error: "Missing required fields" },
        { status: 400 },
      );
    }

    // Check if employee exists, if not create one
    let employee = await prisma.employee.findUnique({
      where: { email },
    });

    if (!employee) {
      employee = await prisma.employee.create({
        data: {
          firstName,
          lastName,
          email,
          phone,
          address,
        },
      });
    } else {
      // Update employee information if it exists
      employee = await prisma.employee.update({
        where: { id: employee.id },
        data: {
          firstName,
          lastName,
          phone,
          address,
        },
      });
    }

    // Create auto loan application
    const autoLoan = await prisma.autoLoan.create({
      data: {
        employeeId: employee.id,
        dniNumber,
        dateOfBirth: new Date(dateOfBirth),
        incomeSource,
        loanAmount,
        totalPrice,
        tradeInValue,
        downPayment,
        remainingPrice,
        niv,
        newPreowned,
        year,
        make,
        model,
        trim,
        status: "submitted",
        documents: {
          create: files.map((file: { originalname: string; path: string }) => ({
            fileName: file.originalname,
            filePath: file.path,
          })),
        },
      },
      include: { documents: true, employee: true },
    });

    // Notify external webhook about the new claim
    try {
      const webhookUrl = `https://n8n-v1.uniphoredemos.com/webhook/create-loan?id=${autoLoan.id}`;
      const webhookResponse = await fetch(webhookUrl, {
        method: "GET",
        headers: {
          "api-key": "NkrZPco7C1tVn6",
        },
      });

      if (!webhookResponse.ok) {
        console.error(
          "Webhook notification failed:",
          await webhookResponse.text(),
        );
      }
    } catch (webhookError) {
      // Log the error but don't fail the claim submission
      console.error("Error notifying webhook:", webhookError);
    }

    return NextResponse.json({ success: true, autoLoan });
  } catch (err) {
    console.error("Error submitting auto loan application:", err);
    const errorMessage = err instanceof Error ? err.message : "Server error";
    return NextResponse.json({ error: errorMessage }, { status: 500 });
  }
}
