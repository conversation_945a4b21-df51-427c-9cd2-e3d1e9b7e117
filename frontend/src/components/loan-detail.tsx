"use client";

import { useState, useEffect } from "react";
import { <PERSON>, <PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Textarea } from "@/components/ui/textarea";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { toast } from "sonner";
import Link from "next/link";
import { DocumentViewerModal } from "@/components/document-viewer-modal";
import { usePageStore } from "@/store/usePageStore";
import { FileDropzone } from "@/components/ui/file-dropzone";
import { FileValidationOptions } from "@/lib/utils";

interface Employee {
  id: number;
  firstName: string;
  lastName: string;
  email: string;
  phone: string | null;
  address: string | null;
  employerName: string | null;
  groupId: string | null;
  memberId: string | null;
}

interface AutoLoanDocument {
  id: number;
  autoLoanId: number;
  fileName: string;
  filePath: string;
  uploadedAt: string;
}

interface AutoLoan {
  id: number;
  employeeId: number;
  employee: Employee;
  dniNumber: string;
  dateOfBirth: string;
  incomeSource: string;
  loanAmount: number;
  totalPrice: number;
  tradeInValue: number;
  downPayment: number;
  remainingPrice: number;
  niv: string;
  newPreowned: string;
  year: number;
  make: string;
  model: string;
  trim: string;
  applicationDate: string;
  status: string;
  documents: AutoLoanDocument[];
}

interface LoanDetailProps {
  id: string;
}

export function LoanDetail({ id }: LoanDetailProps) {
  const [loan, setLoan] = useState<AutoLoan | null>(null);
  const [loading, setLoading] = useState(true);
  const [selectedStatus, setSelectedStatus] = useState<string>("");
  const [comment, setComment] = useState<string>("");
  const [isUpdating, setIsUpdating] = useState(false);
  const [selectedDocument, setSelectedDocument] = useState<AutoLoanDocument | null>(null);
  const [isDocumentModalOpen, setIsDocumentModalOpen] = useState(false);
  const [files, setFiles] = useState<File[]>([]);
  const [fileErrors, setFileErrors] = useState<string[]>([]);
  const [isUploading, setIsUploading] = useState(false);

  const { setLoanDetailPageOpen } = usePageStore();

  useEffect(() => {
    setLoanDetailPageOpen(true);
    return () => setLoanDetailPageOpen(false);
  }, [setLoanDetailPageOpen]);

  useEffect(() => {
    const fetchLoan = async () => {
      try {
        const response = await fetch(`/api/auto-loans/${id}`);
        if (!response.ok) {
          throw new Error("Failed to fetch loan");
        }
        const data = await response.json();
        setLoan(data);
        setSelectedStatus(data.status.toLowerCase());
      } catch (error) {
        console.error("Error fetching loan:", error);
        toast.error("Failed to load loan details");
      } finally {
        setLoading(false);
      }
    };

    fetchLoan();
  }, [id]);

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString();
  };

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat("en-US", {
      style: "currency",
      currency: "USD",
    }).format(amount);
  };

  const handleStatusUpdate = async () => {
    if (!loan) return;

    setIsUpdating(true);
    try {
      const response = await fetch(`/api/auto-loans/${loan.id}`, {
        method: "PUT",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          status: selectedStatus,
        }),
      });

      if (!response.ok) {
        throw new Error("Failed to update loan status");
      }

      const updatedLoan = await response.json();
      setLoan(updatedLoan);
      toast.success("Loan status updated successfully");
    } catch (error) {
      console.error("Error updating loan status:", error);
      toast.error("Failed to update loan status");
    } finally {
      setIsUpdating(false);
    }
  };

  const handleDocumentClick = (document: AutoLoanDocument) => {
    setSelectedDocument(document);
    setIsDocumentModalOpen(true);
  };

  const fileValidationOptions: FileValidationOptions = {
    maxSizeMB: 10,
    allowedTypes: ["image/jpeg", "image/png", "application/pdf", "image/jpg"],
    maxFiles: 10,
  };

  const handleFileUpload = async () => {
    if (files.length === 0) {
      toast.error("Please select files to upload");
      return;
    }

    setIsUploading(true);
    try {
      const formData = new FormData();
      files.forEach((file) => {
        formData.append("files", file);
      });

      const uploadResponse = await fetch("/api/upload", {
        method: "POST",
        body: formData,
      });

      if (!uploadResponse.ok) {
        throw new Error("Failed to upload files");
      }

      const uploadResult = await uploadResponse.json();

      // Add documents to the loan
      const response = await fetch(`/api/auto-loans/${loan?.id}/documents`, {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          documents: uploadResult.files.map((file: any) => ({
            fileName: file.originalname,
            filePath: file.path,
          })),
        }),
      });

      if (!response.ok) {
        throw new Error("Failed to add documents to loan");
      }

      // Refresh loan data
      const updatedLoan = await response.json();
      setLoan(updatedLoan);
      setFiles([]);
      toast.success("Documents uploaded successfully");
    } catch (error) {
      console.error("Error uploading documents:", error);
      toast.error("Failed to upload documents");
    } finally {
      setIsUploading(false);
    }
  };

  if (loading) {
    return (
      <div className="flex justify-center p-8">Loading loan details...</div>
    );
  }

  if (!loan) {
    return (
      <div className="flex flex-col items-center justify-center p-8 space-y-4">
        <h2 className="text-xl font-semibold">Loan not found</h2>
        <p>The loan application you are looking for does not exist or has been removed.</p>
        <Link href="/loan-management">
          <Button>Back to Loan Management</Button>
        </Link>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      <div className="flex justify-between items-center">
        <CardHeader className="px-0 flex-1">
          <CardTitle>Loan Application #{loan.id} Details</CardTitle>
        </CardHeader>
        <Link href="/loan-management">
          <Button variant="outline">Back to Loan List</Button>
        </Link>
      </div>

      {/* Applicant Information */}
      <Card>
        <CardHeader>
          <CardTitle>Applicant Information</CardTitle>
        </CardHeader>
        <CardContent className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div>
            <p className="text-sm font-medium text-gray-500">Name</p>
            <p className="text-sm">{loan.employee.firstName} {loan.employee.lastName}</p>
          </div>
          <div>
            <p className="text-sm font-medium text-gray-500">Email</p>
            <p className="text-sm">{loan.employee.email}</p>
          </div>
          <div>
            <p className="text-sm font-medium text-gray-500">Phone</p>
            <p className="text-sm">{loan.employee.phone || "N/A"}</p>
          </div>
          <div>
            <p className="text-sm font-medium text-gray-500">Address</p>
            <p className="text-sm">{loan.employee.address || "N/A"}</p>
          </div>
          <div>
            <p className="text-sm font-medium text-gray-500">DNI Number</p>
            <p className="text-sm">{loan.dniNumber}</p>
          </div>
          <div>
            <p className="text-sm font-medium text-gray-500">Date of Birth</p>
            <p className="text-sm">{formatDate(loan.dateOfBirth)}</p>
          </div>
          <div>
            <p className="text-sm font-medium text-gray-500">Income Source</p>
            <p className="text-sm">{loan.incomeSource}</p>
          </div>
        </CardContent>
      </Card>

      {/* Vehicle Information */}
      <Card>
        <CardHeader>
          <CardTitle>Vehicle Information</CardTitle>
        </CardHeader>
        <CardContent className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div>
            <p className="text-sm font-medium text-gray-500">Vehicle</p>
            <p className="text-sm">{loan.year} {loan.make} {loan.model}</p>
          </div>
          <div>
            <p className="text-sm font-medium text-gray-500">Trim</p>
            <p className="text-sm">{loan.trim}</p>
          </div>
          <div>
            <p className="text-sm font-medium text-gray-500">Condition</p>
            <p className="text-sm">{loan.newPreowned}</p>
          </div>
          <div>
            <p className="text-sm font-medium text-gray-500">NIV</p>
            <p className="text-sm">{loan.niv}</p>
          </div>
        </CardContent>
      </Card>

      {/* Financial Information */}
      <Card>
        <CardHeader>
          <CardTitle>Financial Information</CardTitle>
        </CardHeader>
        <CardContent className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div>
            <p className="text-sm font-medium text-gray-500">Total Price</p>
            <p className="text-sm">{formatCurrency(loan.totalPrice)}</p>
          </div>
          <div>
            <p className="text-sm font-medium text-gray-500">Trade-in Value</p>
            <p className="text-sm">{formatCurrency(loan.tradeInValue)}</p>
          </div>
          <div>
            <p className="text-sm font-medium text-gray-500">Down Payment</p>
            <p className="text-sm">{formatCurrency(loan.downPayment)}</p>
          </div>
          <div>
            <p className="text-sm font-medium text-gray-500">Loan Amount</p>
            <p className="text-sm font-semibold text-lg">{formatCurrency(loan.loanAmount)}</p>
          </div>
          <div>
            <p className="text-sm font-medium text-gray-500">Remaining Price</p>
            <p className="text-sm">{formatCurrency(loan.remainingPrice)}</p>
          </div>
        </CardContent>
      </Card>

      {/* Application Status */}
      <Card>
        <CardHeader>
          <CardTitle>Application Status</CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <p className="text-sm font-medium text-gray-500">Application Date</p>
              <p className="text-sm">{formatDate(loan.applicationDate)}</p>
            </div>
            <div>
              <p className="text-sm font-medium text-gray-500">Current Status</p>
              <p className="text-sm font-semibold">{loan.status}</p>
            </div>
          </div>

          <div className="flex gap-4 items-end">
            <div className="flex-1">
              <label className="text-sm font-medium text-gray-500">Update Status</label>
              <Select value={selectedStatus} onValueChange={setSelectedStatus}>
                <SelectTrigger>
                  <SelectValue placeholder="Select status" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="submitted">Submitted</SelectItem>
                  <SelectItem value="pending">Pending</SelectItem>
                  <SelectItem value="approved">Approved</SelectItem>
                  <SelectItem value="rejected">Rejected</SelectItem>
                </SelectContent>
              </Select>
            </div>
            <Button
              onClick={handleStatusUpdate}
              disabled={isUpdating || selectedStatus === loan.status.toLowerCase()}
            >
              {isUpdating ? "Updating..." : "Update Status"}
            </Button>
          </div>
        </CardContent>
      </Card>

      {/* Documents */}
      <Card>
        <CardHeader>
          <CardTitle>Documents ({loan.documents.length})</CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          {loan.documents.length > 0 ? (
            <div className="border rounded-md overflow-hidden">
              <ul className="divide-y divide-gray-200">
                {loan.documents.map((document) => (
                  <li
                    key={document.id}
                    className="flex items-center justify-between p-4 hover:bg-gray-50 cursor-pointer"
                    onClick={() => handleDocumentClick(document)}
                  >
                    <div className="flex-1">
                      <p className="text-sm font-medium">{document.fileName}</p>
                      <p className="text-xs text-gray-500">
                        Uploaded: {formatDate(document.uploadedAt)}
                      </p>
                    </div>
                    <div className="flex gap-2">
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={(e) => {
                          e.stopPropagation();
                          handleDocumentClick(document);
                        }}
                      >
                        View
                      </Button>
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={(e) => {
                          e.stopPropagation();
                          // Create download URL and trigger download
                          const downloadUrl = `/api/auto-loans/${loan.id}/documents/${document.id}/download`;
                          window.open(downloadUrl, '_blank');
                        }}
                      >
                        Download
                      </Button>
                    </div>
                  </li>
                ))}
              </ul>
            </div>
          ) : (
            <p className="text-sm text-gray-500">No documents attached</p>
          )}

          {/* File Upload */}
          <div className="border-t pt-4">
            <h4 className="font-medium mb-2">Upload Additional Documents</h4>
            <FileDropzone
              files={files}
              onChange={setFiles}
              onError={setFileErrors}
              validationOptions={fileValidationOptions}
              accept={fileValidationOptions.allowedTypes?.join(",")}
              maxFiles={fileValidationOptions.maxFiles}
            />
            {files.length > 0 && (
              <Button
                onClick={handleFileUpload}
                disabled={isUploading}
                className="mt-2"
              >
                {isUploading ? "Uploading..." : "Upload Documents"}
              </Button>
            )}
          </div>
        </CardContent>
      </Card>

      {/* Document Viewer Modal */}
      {selectedDocument && (
        <DocumentViewerModal
          documentId={selectedDocument.id}
          fileName={selectedDocument.fileName}
          loanId={loan?.id} // Pass loan ID for auto-loan documents
          onClose={() => {
            setIsDocumentModalOpen(false);
            setSelectedDocument(null);
          }}
        />
      )}
    </div>
  );
}
