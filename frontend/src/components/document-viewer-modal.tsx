"use client";

import { useState, useEffect, useRef } from "react";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { toast } from "sonner";
import { PdfViewer } from "@/components/pdf-viewer";

interface DocumentViewerModalProps {
  documentId: number;
  claimId?: number;
  policyId?: number;
  loanId?: number;
  fileName: string;
  onClose: () => void;
}

export function DocumentViewerModal({ documentId, claimId, policyId, loanId, fileName, onClose }: DocumentViewerModalProps) {
  const [loading, setLoading] = useState(true);
  const [documentUrl, setDocumentUrl] = useState<string | null>(null);
  const [documentType, setDocumentType] = useState<"image" | "pdf" | "other">("other");
  const [isVisible, setIsVisible] = useState(false);
  const modalRef = useRef<HTMLDivElement>(null);

  useEffect(() => {
    const prepareDocument = async () => {
      try {
        setLoading(true);

        // Determine the API endpoint based on document type
        const apiEndpoint = policyId
          ? `/api/policies/${policyId}/documents/${documentId}/download?view=true`
          : loanId
          ? `/api/auto-loans/${loanId}/documents/${documentId}/download?view=true`
          : `/api/claims/${claimId}/documents/${documentId}/download?view=true`;

        // Determine document type first
        const fileExtension = fileName.split('.').pop()?.toLowerCase();
        if (fileExtension === 'pdf') {
          setDocumentType("pdf");
          // For PDFs, use the direct API URL with view=true
          setDocumentUrl(apiEndpoint);
        } else if (['jpg', 'jpeg', 'png', 'gif', 'bmp', 'webp'].includes(fileExtension || '')) {
          setDocumentType("image");

          // For images, still use blob to handle them properly
          const response = await fetch(apiEndpoint);
          if (!response.ok) {
            throw new Error("Failed to fetch document");
          }

          const blob = await response.blob();
          const mimeType = `image/${fileExtension === 'jpg' ? 'jpeg' : fileExtension}`;
          const typedBlob = new Blob([blob], { type: mimeType });
          const url = URL.createObjectURL(typedBlob);
          setDocumentUrl(url);
        } else {
          setDocumentType("other");
          // For other files, prepare for download only
          setDocumentUrl(null);
        }
      } catch (error) {
        console.error("Error preparing document:", error);
        toast.error("Failed to load document");
      } finally {
        setLoading(false);
      }
    };

    prepareDocument();

    // Set modal to visible after a small delay to ensure animation works
    setTimeout(() => {
      setIsVisible(true);
    }, 50);

    // Cleanup function to revoke object URL when component unmounts
    return () => {
      if (documentUrl && documentType === "image") {
        // Only revoke if it's a blob URL (for images)
        URL.revokeObjectURL(documentUrl);
      }
    };
  }, [claimId, policyId, loanId, documentId, fileName, onClose]);

  const handleDownload = async () => {
    try {
      // Determine the API endpoint based on document type
      const apiEndpoint = policyId
        ? `/api/policies/${policyId}/documents/${documentId}/download`
        : loanId
        ? `/api/auto-loans/${loanId}/documents/${documentId}/download`
        : `/api/claims/${claimId}/documents/${documentId}/download`;

      // Explicitly fetch for download (without view parameter)
      const response = await fetch(apiEndpoint);
      if (!response.ok) {
        throw new Error("Failed to download document");
      }

      const blob = await response.blob();
      const url = URL.createObjectURL(blob);

      const a = document.createElement('a');
      a.href = url;
      a.download = fileName;
      document.body.appendChild(a);
      a.click();
      document.body.removeChild(a);

      // Clean up
      URL.revokeObjectURL(url);

      toast.success(`Downloaded ${fileName}`);
    } catch (error) {
      console.error("Error downloading document:", error);
      toast.error("Failed to download document");
    }
  };

  return (
    <div className="fixed inset-0 pointer-events-none flex justify-end z-50">
      {/* Invisible overlay to capture clicks outside the modal */}
      <div
        className="absolute inset-0 pointer-events-auto"
        onClick={() => {
          setIsVisible(false);
          setTimeout(() => {
            onClose();
          }, 300);
        }}
      />
      <div
        className="w-[75%] h-full transform transition-transform duration-300 ease-in-out pointer-events-auto"
        style={{
          transform: isVisible ? 'translateX(0)' : 'translateX(100%)',
          boxShadow: '-4px 0 15px rgba(0, 0, 0, 0.1)'
        }}
        ref={modalRef}
      >
        <Card className="h-full w-full flex flex-col rounded-l-xl rounded-r-none bg-white py-0 document-viewer-modal">
          <CardHeader className="flex flex-row items-center justify-between bg-white border-b py-2 px-4">
            <CardTitle className="truncate max-w-[calc(100%-100px)] text-black font-semibold">{fileName}</CardTitle>
            <div className="flex items-center gap-2">
              <Button variant="outline" size="sm" onClick={handleDownload}>
                Download
              </Button>
              <Button variant="ghost" size="sm" onClick={() => {
                setIsVisible(false);
                setTimeout(() => {
                  onClose();
                }, 300);
              }}>×</Button>
            </div>
          </CardHeader>
          <CardContent className="flex-1 overflow-auto p-0 gap-0">
            {loading ? (
              <div className="flex items-center justify-center h-full">
                <p>Loading document...</p>
              </div>
            ) : documentUrl ? (
              <div className="h-full flex items-center justify-center bg-gray-100">
                {documentType === "image" ? (
                  <img
                    src={documentUrl}
                    alt={fileName}
                    className="max-w-full max-h-full object-contain"
                  />
                ) : documentType === "pdf" ? (
                  <div className="w-full h-full">
                    <PdfViewer
                      url={documentUrl || ''}
                      fileName={fileName}
                      onDownload={handleDownload}
                    />
                  </div>
                ) : (
                  <div className="text-center p-8">
                    <p className="mb-4">This file type cannot be previewed.</p>
                    <Button onClick={handleDownload}>Download File</Button>
                  </div>
                )}
              </div>
            ) : (
              <div className="flex items-center justify-center h-full">
                <p>Failed to load document.</p>
              </div>
            )}
          </CardContent>
        </Card>
      </div>
    </div>
  );
}
