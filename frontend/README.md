This is a [Next.js](https://nextjs.org) project bootstrapped with [`create-next-app`](https://nextjs.org/docs/app/api-reference/cli/create-next-app).

## Database Setup

This application uses PostgreSQL as its database. You have two options for setup:

### Option 1: Docker Compose (Recommended)

The easiest way to get started is using Docker Compose, which will set up both PostgreSQL and the application:

```bash
# From the project root directory
docker-compose up -d

# View logs
docker-compose logs -f

# Stop services
docker-compose down
```

### Option 2: Local Development with External PostgreSQL

If you prefer to run the application locally:

```bash
# Start only PostgreSQL
docker-compose up -d postgres

# Install dependencies
npm install --legacy-peer-deps

# Run database migrations
npx prisma migrate deploy

# Start the development server
npm run dev
```

## Environment Variables

Create a `.env` file with the following configuration:

```
DATABASE_URL="postgresql://claims_user:claims_password@localhost:5432/claims_customer?schema=public"
```

## Getting Started

After setting up the database, open [http://localhost:3000](http://localhost:3000) with your browser to see the result.

You can start editing the page by modifying `app/page.tsx`. The page auto-updates as you edit the file.

This project uses [`next/font`](https://nextjs.org/docs/app/building-your-application/optimizing/fonts) to automatically optimize and load [Geist](https://vercel.com/font), a new font family for Vercel.

## File Upload Limits

The application supports the following file upload limits:

- Maximum file size: 25MB per file
- Maximum number of files: 10 files per upload
- Supported file types: PDF, PNG, JPEG, JPG, GIF
- Minimum required files: 1 file

These limits are configured in:
- `src/app/submit-claim/page.tsx` - Client-side validation
- `src/lib/utils.ts` - Default validation settings
- `next.config.ts` - Server-side API route body size limit (30MB)

## Learn More

To learn more about Next.js, take a look at the following resources:

- [Next.js Documentation](https://nextjs.org/docs) - learn about Next.js features and API.
- [Learn Next.js](https://nextjs.org/learn) - an interactive Next.js tutorial.

You can check out [the Next.js GitHub repository](https://github.com/vercel/next.js) - your feedback and contributions are welcome!

## Deploy on Vercel

The easiest way to deploy your Next.js app is to use the [Vercel Platform](https://vercel.com/new?utm_medium=default-template&filter=next.js&utm_source=create-next-app&utm_campaign=create-next-app-readme) from the creators of Next.js.

Check out our [Next.js deployment documentation](https://nextjs.org/docs/app/building-your-application/deploying) for more details.
