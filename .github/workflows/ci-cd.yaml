name: CI/CD Pipeline

on:
  push:
    branches: [main]
  pull_request:
    branches: [main]
  workflow_dispatch: # Allow manual triggering

env:
  DOCKER_REGISTRY: reg.uniphoredemos.com
  IMAGE_NAME: claims-customer
  KUBE_NAMESPACE: claims-demo

jobs:
  build-and-test:
    runs-on: ubuntu-latest
    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Set up Node.js
        uses: actions/setup-node@v4
        with:
          node-version: "20"
          cache: "npm"
          cache-dependency-path: frontend/package-lock.json

      - name: Install dependencies
        working-directory: ./frontend
        run: npm ci --legacy-peer-deps

      - name: Generate Prisma client
        working-directory: ./frontend
        run: npx prisma generate

      - name: Run tests
        working-directory: ./frontend
        run: npm test || echo "No tests specified"

  build-and-push-image:
    needs: build-and-test
    runs-on: ubuntu-latest
    # Only run on main branch push, not on PRs
    if: github.event_name == 'push' || github.event_name == 'workflow_dispatch'
    permissions:
      contents: read
      packages: write

    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Set up QEMU
        uses: docker/setup-qemu-action@v3

      - name: Set up Docker Buildx
        uses: docker/setup-buildx-action@v3

      - name: Log in to Private Container Registry
        uses: docker/login-action@v3
        with:
          registry: ${{ env.DOCKER_REGISTRY }}
          username: ${{ secrets.DOCKER_USERNAME }}
          password: ${{ secrets.DOCKER_PASSWORD }}

      - name: Extract metadata for Docker
        id: meta
        uses: docker/metadata-action@v5
        with:
          images: ${{ env.DOCKER_REGISTRY }}/${{ env.IMAGE_NAME }}
          tags: |
            type=sha,format=short
            type=ref,event=branch
            type=ref,event=tag
            latest

      - name: Build and push Docker image
        uses: docker/build-push-action@v6
        with:
          platforms: linux/amd64
          context: ./frontend
          push: true
          tags: ${{ steps.meta.outputs.tags }}
          labels: ${{ steps.meta.outputs.labels }}
          cache-from: type=gha
          cache-to: type=gha,mode=max

  deploy-to-kubernetes:
    needs: build-and-push-image
    runs-on: ubuntu-latest
    if: github.event_name == 'push' || github.event_name == 'workflow_dispatch'

    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Set image tag
        id: set-image-tag
        run: |
          SHORT_SHA=$(echo ${{ github.sha }} | cut -c1-7)
          echo "IMAGE_TAG=${SHORT_SHA}" >> $GITHUB_ENV

      # - name: Set up kubeconfig
      #   uses: azure/k8s-set-context@v3
      #   with:
      #     method: kubeconfig
      #     kubeconfig: ${{ secrets.KUBE_CONFIG }}
      - name: Install kubectl
        uses: azure/setup-kubectl@v3

      - name: Create .kube/config
        run: |
          mkdir -p ~/.kube
          echo "${{ secrets.KUBE_CONFIG }}" > ~/.kube/config

      - name: Apply namespace
        run: |
          kubectl apply -f k8s/namespace.yaml

      - name: Check existing pods
        run: |
          # Just check the existing pods without deleting them
          echo "Checking existing pods..."
          kubectl get pods -l app=claims-customer -n ${{ env.KUBE_NAMESPACE }}
          echo "Will use rolling update strategy instead of pod deletion"

      - name: Update Kubernetes resources
        run: |
          # Replace placeholders in deployment.yaml
          sed -i "s|\${DOCKER_REGISTRY}|${{ env.DOCKER_REGISTRY }}|g" k8s/deployment.yaml
          sed -i "s|\${IMAGE_TAG}|${{ env.IMAGE_TAG }}|g" k8s/deployment.yaml
          sed -i "s|\${AIRTABLE_BASE_ID}|${{ secrets.AIRTABLE_BASE_ID }}|g" k8s/configmap.yaml
          sed -i "s|\${AIRTABLE_API_KEY}|${{ secrets.AIRTABLE_API_KEY }}|g" k8s/configmap.yaml
          sed -i "s|\${XSTREAM_CLIENT_ID}|${{ secrets.XSTREAM_CLIENT_ID }}|g" k8s/configmap.yaml
          sed -i "s|\${XSTREAM_CLIENT_SECRET}|${{ secrets.XSTREAM_CLIENT_SECRET }}|g" k8s/configmap.yaml  

          # Replace the date placeholder in the change-cause annotation
          CURRENT_DATE=$(date -u +"%Y-%m-%d %H:%M:%S UTC")
          sed -i "s|\$(date)|$CURRENT_DATE|g" k8s/deployment.yaml

          # Apply Kubernetes manifests
          kubectl apply -f k8s/configmap.yaml
          kubectl apply -f k8s/persistent-volume.yaml
          kubectl apply -f k8s/database-pvc.yaml
          kubectl apply -f k8s/docker-registry-secret.yaml
          # Apply deployment without deleting it first to use rolling update
          kubectl apply -f k8s/postgres-pvc.yaml
          kubectl apply -f k8s/postgres-init-configmap.yaml
          kubectl apply -f k8s/postgres-deployment.yaml
          kubectl apply -f k8s/deployment.yaml
          kubectl apply -f k8s/service.yaml
          kubectl apply -f k8s/ingress.yaml

      - name: Verify deployment
        run: |
          kubectl rollout status deployment/claims-customer -n claims-demo --timeout=180s
