version: '3.8'

services:
  postgres:
    image: postgres:15-alpine
    container_name: claims-postgres
    restart: unless-stopped
    environment:
      POSTGRES_DB: claims_customer
      POSTGRES_USER: claims_user
      POSTGRES_PASSWORD: claims_password
      PGDATA: /var/lib/postgresql/data/pgdata
    ports:
      - "5432:5432"
    volumes:
      - postgres_data:/var/lib/postgresql/data
      - ./scripts/init-postgres.sql:/docker-entrypoint-initdb.d/init.sql
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U claims_user -d claims_customer"]
      interval: 10s
      timeout: 5s
      retries: 5

  claims-app:
    build:
      context: ./frontend
      dockerfile: Dockerfile
    container_name: claims-app
    restart: unless-stopped
    ports:
      - "3000:3000"
    environment:
      DATABASE_URL: "******************************************************/claims_customer?schema=public"
      NODE_ENV: production
    depends_on:
      postgres:
        condition: service_healthy
    volumes:
      - ./frontend/uploads:/app/uploads
    healthcheck:
      test: ["CMD-SHELL", "curl -f http://localhost:3000 || exit 1"]
      interval: 30s
      timeout: 10s
      retries: 3

volumes:
  postgres_data:
    driver: local
